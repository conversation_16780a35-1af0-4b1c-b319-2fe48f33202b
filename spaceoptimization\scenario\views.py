from collections import defaultdict
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .serializers import (
    ScenarioMetadSerializer, 
    ClusterDataSerializer, 
    ClusterRequestSerializer,
    OutlierUpdateSerializer,
)
from .model_map import CLUSTER_MODEL_MAP
from .models import ScenarioStatus,AppHbPreopt
from .serializers import ScenarioMetadSerializer, ClusterDataSerializer
from django.utils import timezone
from django.db import transaction


class ScenarioCreateAPIView(APIView):


    def post(self, request):
        serializer = ScenarioMetadSerializer(data=request.data)
        try:
            with transaction.atomic():
                if serializer.is_valid(raise_exception=True):
                    scenario = serializer.save(
                        created_at=timezone.now(),
                        updated_at=timezone.now()
                    )
                    # Create scenario status
                    ScenarioStatus.objects.create(scenario=scenario)

                    return Response(
                        {'message': 'Scenario created successfully'},
                        status=status.HTTP_201_CREATED
                    )
        except Exception as e:
            # If any error occurs, transaction will rollback automatically
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class ClusterByLocationView(APIView):

    
    def post(self, request):
        # Step 1: Validate input
        input_serializer = ClusterRequestSerializer(data=request.data)
        if not input_serializer.is_valid():
            return Response(input_serializer.errors, status=400)

        validated = input_serializer.validated_data
        concept = validated["concept"]
        territory = validated["territory"]
        loc_codes = validated["loc_codes"]

        # Step 2: Resolve model from map
        table_name = f"de_{concept.lower()}_{territory.lower()}_cluster"
        ClusterModel = CLUSTER_MODEL_MAP.get(table_name)
        if not ClusterModel:
            return Response({"error": f"Unsupported cluster table: {table_name}"}, status=400)

        # Step 3: Fetch data
        try:
            filtered_data = ClusterModel.objects.filter(loc_cd__in=loc_codes)
        except Exception as e:
            return Response({"error": f"Database error: {str(e)}"}, status=500)

        # Step 4: Group by cluster_num
        grouped = defaultdict(list)
        for row in filtered_data:
            # Dynamically assign model to serializer
            ClusterDataSerializer.Meta.model = ClusterModel
            data = ClusterDataSerializer(row).data
            grouped[row.cluster_num].append(data)

        return Response(grouped)


class OutlierAPIView(APIView):
    def get(self, request):
        """Get outlier records with optional filtering by subclass and store"""
        try:
            sub_clss_nm = request.query_params.get('subclass')
            loc_cd = request.query_params.get('store')

            filters = {}
            if sub_clss_nm:
                filters['sub_clss_nm'] = sub_clss_nm
            if loc_cd:
                try:
                    filters['loc_cd'] = int(loc_cd)
                except ValueError:
                    return Response(
                        {
                            'error': 'Invalid store parameter',
                            'details': 'store parameter must be a valid number'
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

            outliers = AppHbPreopt.objects.filter(**filters)

            if not outliers.exists():
                return Response(
                    {
                        'message': 'No outlier records found',
                        'data': []
                    },
                    status=status.HTTP_200_OK
                )

            data = outliers.values(
                'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm','loc_nm', 'loc_cd', 'outlier_status', 'outlier_status_final',
                'lm_contribution_in_store', 'month', 'total_lm', 'gmv_per_day',
                'suggested_total_lm'
            )

            return Response(
                {
                    'count': outliers.count(),
                    'data': list(data)
                },
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {
                    'error': 'Internal server error',
                    'details': str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def patch(self, request):
        """Update outlier status for multiple records with all-or-nothing validation and updates"""
        # Handle both single object and array of objects
        data = request.data

        if not data:
            return Response(
                {
                    'error': 'Invalid input data',
                    'details': 'Request data cannot be empty'
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Step 1: Validate all objects first
        validated_objects = []
        validation_errors = []

        for index, item in enumerate(data):
            serializer = OutlierUpdateSerializer(data=item)
            if not serializer.is_valid():
                validation_errors.append({
                    'index': index,
                    'data': item,
                    'errors': serializer.errors
                })
            else:
                validated_objects.append(serializer.validated_data)

        # If any validation errors, return them all
        if validation_errors:
            return Response(
                {
                    'error': 'Validation failed for one or more records',
                    'details': validation_errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        # Step 2: Check if all records exist before updating any
        records_to_update = []
        missing_records = []

        try:
            for index, validated_data in enumerate(validated_objects):
                sub_clss_nm = validated_data['sub_clss_nm']
                loc_cd = validated_data['loc_cd']
                month = validated_data['month']

                try:
                    outlier_record = AppHbPreopt.objects.get(
                        sub_clss_nm=sub_clss_nm,
                        loc_cd=int(loc_cd),
                        month=month
                    )
                    records_to_update.append((outlier_record, validated_data))
                except AppHbPreopt.DoesNotExist:
                    missing_records.append({
                        'index': index,
                        'sub_clss_nm': sub_clss_nm,
                        'loc_cd': loc_cd,
                        'month': month
                    })
                except ValueError as e:
                    return Response(
                        {
                            'error': 'Invalid data format',
                            'details': f'loc_cd must be a valid integer at index {index}: {str(e)}'
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # If any records are missing, return error
            if missing_records:
                return Response(
                    {
                        'error': 'One or more records not found',
                        'details': missing_records
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            # Step 3: Update all records in a transaction (all-or-nothing)
            updated_records = []
            with transaction.atomic():
                for outlier_record, validated_data in records_to_update:
                    outlier_record.outlier_status = validated_data['outlier_status']
                    outlier_record.outlier_status_final = validated_data['outlier_status_final']
                    outlier_record.save(update_fields=['outlier_status', 'outlier_status_final'])

                    updated_records.append({
                        'sub_clss_nm': outlier_record.sub_clss_nm,
                        'loc_cd': outlier_record.loc_cd,
                        'outlier_status': outlier_record.outlier_status,
                        'outlier_status_final': outlier_record.outlier_status_final,
                        'month': outlier_record.month
                    })

            return Response(
                {
                    'message': f'{len(updated_records)} outlier record(s) updated successfully',
                    'count': len(updated_records),
                    'data': updated_records
                },
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {
                    'error': 'Internal server error',
                    'details': str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class healthAPIView(APIView):
    def get(self, request):
        return Response({"message": "Hello, world!"})
