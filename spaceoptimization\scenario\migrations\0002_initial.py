# Generated by Django 4.2 on 2025-08-03 13:34

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('scenario', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppHbStrCluster',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scenario_id', models.IntegerField()),
                ('territory_nm', models.CharField(max_length=64)),
                ('loc_cd', models.CharField(max_length=32)),
                ('loc_nm', models.Char<PERSON>ield(max_length=128)),
                ('stnd_trrtry_nm', models.Char<PERSON>ield(max_length=64)),
                ('rgn_nm', models.CharField(max_length=64)),
                ('revenue', models.DecimalField(decimal_places=1, max_digits=32)),
                ('units', models.DecimalField(decimal_places=1, max_digits=32)),
                ('gmv', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_lm', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_customer', models.BigIntegerField()),
                ('total_invoice', models.BigIntegerField()),
                ('area_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('cluster_num', models.IntegerField()),
                ('volume_contribution', models.JSONField()),
                ('ethnicity_contribution', models.JSONField()),
                ('revenue_per_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('updated_at', models.DateTimeField()),
                ('updated_by', models.IntegerField()),
                ('new_cluster_num', models.IntegerField()),
            ],
            options={
                'db_table': 'app_hb_str_cluster',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AppLsStrCluster',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scenario_id', models.IntegerField()),
                ('territory_nm', models.CharField(max_length=64)),
                ('loc_cd', models.CharField(max_length=32)),
                ('loc_nm', models.CharField(max_length=128)),
                ('stnd_trrtry_nm', models.CharField(max_length=64)),
                ('rgn_nm', models.CharField(max_length=64)),
                ('revenue', models.DecimalField(decimal_places=1, max_digits=32)),
                ('units', models.DecimalField(decimal_places=1, max_digits=32)),
                ('gmv', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_lm', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_customer', models.BigIntegerField()),
                ('total_invoice', models.BigIntegerField()),
                ('area_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('cluster_num', models.IntegerField()),
                ('volume_contribution', models.JSONField()),
                ('ethnicity_contribution', models.JSONField()),
                ('revenue_per_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('updated_at', models.DateTimeField()),
                ('updated_by', models.IntegerField()),
                ('new_cluster_num', models.IntegerField()),
            ],
            options={
                'db_table': 'app_ls_str_cluster',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AppSpStrCluster',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scenario_id', models.IntegerField()),
                ('territory_nm', models.CharField(max_length=64)),
                ('loc_cd', models.CharField(max_length=32)),
                ('loc_nm', models.CharField(max_length=128)),
                ('stnd_trrtry_nm', models.CharField(max_length=64)),
                ('rgn_nm', models.CharField(max_length=64)),
                ('revenue', models.DecimalField(decimal_places=1, max_digits=32)),
                ('units', models.DecimalField(decimal_places=1, max_digits=32)),
                ('gmv', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_lm', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_customer', models.BigIntegerField()),
                ('total_invoice', models.BigIntegerField()),
                ('area_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('cluster_num', models.IntegerField()),
                ('volume_contribution', models.JSONField()),
                ('ethnicity_contribution', models.JSONField()),
                ('revenue_per_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('updated_at', models.DateTimeField()),
                ('updated_by', models.IntegerField()),
                ('new_cluster_num', models.IntegerField()),
            ],
            options={
                'db_table': 'app_sp_str_cluster',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DeHbAeCluster',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('loc_cd', models.CharField(max_length=20)),
                ('loc_nm', models.CharField(max_length=100)),
                ('rgn_nm', models.CharField(max_length=100)),
                ('revenue', models.DecimalField(decimal_places=1, max_digits=32)),
                ('units', models.DecimalField(decimal_places=1, max_digits=32)),
                ('gmv', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_lm', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_customer', models.BigIntegerField()),
                ('total_invoice', models.BigIntegerField()),
                ('area_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('cluster_num', models.IntegerField()),
                ('volume_contribution', models.JSONField()),
                ('ethnicity_contribution', models.JSONField()),
                ('revenue_per_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('last_update_dt_tm', models.DateTimeField()),
            ],
            options={
                'db_table': 'de_hb_ae_cluster',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DeLsKwCluster',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('loc_cd', models.CharField(max_length=20)),
                ('loc_nm', models.CharField(max_length=100)),
                ('rgn_nm', models.CharField(max_length=100)),
                ('revenue', models.DecimalField(decimal_places=1, max_digits=32)),
                ('units', models.DecimalField(decimal_places=1, max_digits=32)),
                ('gmv', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_lm', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_customer', models.BigIntegerField()),
                ('total_invoice', models.BigIntegerField()),
                ('area_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('cluster_num', models.IntegerField()),
                ('volume_contribution', models.JSONField()),
                ('ethnicity_contribution', models.JSONField()),
                ('revenue_per_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('last_update_dt_tm', models.DateTimeField()),
            ],
            options={
                'db_table': 'de_ls_kw_cluster',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='DeSpEgCluster',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('loc_cd', models.CharField(max_length=20)),
                ('loc_nm', models.CharField(max_length=100)),
                ('rgn_nm', models.CharField(max_length=100)),
                ('revenue', models.DecimalField(decimal_places=1, max_digits=32)),
                ('units', models.DecimalField(decimal_places=1, max_digits=32)),
                ('gmv', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_lm', models.DecimalField(decimal_places=1, max_digits=32)),
                ('total_customer', models.BigIntegerField()),
                ('total_invoice', models.BigIntegerField()),
                ('area_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('cluster_num', models.IntegerField()),
                ('volume_contribution', models.JSONField()),
                ('ethnicity_contribution', models.JSONField()),
                ('revenue_per_sqft', models.DecimalField(decimal_places=1, max_digits=32)),
                ('last_update_dt_tm', models.DateTimeField()),
            ],
            options={
                'db_table': 'de_sp_eg_cluster',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='ScenarioMetad',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('user_id', models.IntegerField()),
                ('season_type', models.CharField(max_length=32)),
                ('eval_type', models.CharField(max_length=32)),
                ('event_name', models.CharField(max_length=128)),
                ('eval_start', models.DateTimeField()),
                ('eval_end', models.DateTimeField()),
                ('ref_start', models.DateTimeField()),
                ('ref_end', models.DateTimeField()),
                ('CNCPT_NM', models.CharField(max_length=64)),
                ('TERRITORY_NM', models.CharField(max_length=64)),
                ('metric', models.CharField(max_length=64)),
                ('sqft_file_id', models.IntegerField(blank=True, null=True)),
                ('mdq_file_id', models.IntegerField(blank=True, null=True)),
                ('cover_file_id', models.IntegerField(blank=True, null=True)),
                ('exclusion_file_id', models.IntegerField(blank=True, null=True)),
                ('loc_cd', models.JSONField()),
                ('created_by', models.IntegerField()),
                ('updated_by', models.IntegerField()),
                ('created_at', models.DateTimeField()),
                ('updated_at', models.DateTimeField()),
            ],
            options={
                'db_table': 'SCENARIO_METAD',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='ScenarioStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(default='CREATED', max_length=64)),
            ],
            options={
                'db_table': 'SCENARIO_STATUS',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AppHbPreopt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('territory_nm', models.CharField(max_length=20)),
                ('loc_cd', models.IntegerField()),
                ('loc_nm', models.CharField(max_length=100)),
                ('stnd_trrtry_nm', models.CharField(max_length=100)),
                ('rgn_nm', models.CharField(max_length=100)),
                ('grp_nm', models.CharField(max_length=100)),
                ('dpt_nm', models.CharField(max_length=100)),
                ('clss_nm', models.CharField(max_length=100)),
                ('sub_clss_nm', models.CharField(max_length=100)),
                ('month', models.IntegerField()),
                ('mnth_avg_soh', models.FloatField()),
                ('mnth_avg_itm_cnt', models.FloatField()),
                ('mnth_avg_optn_cnt', models.FloatField()),
                ('mnth_end_soh', models.FloatField()),
                ('mnth_end_itm_cnt', models.IntegerField()),
                ('mnth_end_optn_cnt', models.IntegerField()),
                ('net_sls_amt', models.FloatField()),
                ('rtl_qty', models.FloatField()),
                ('gmv', models.FloatField()),
                ('inv_cnt', models.IntegerField()),
                ('cust_cnt', models.IntegerField()),
                ('str_visits', models.IntegerField()),
                ('str_cust_cnt', models.IntegerField()),
                ('cust_pen', models.FloatField()),
                ('spc', models.FloatField()),
                ('margin_perc', models.FloatField()),
                ('asp', models.FloatField()),
                ('sls_per_inv', models.FloatField()),
                ('units_per_inv', models.FloatField()),
                ('ros', models.FloatField()),
                ('cover', models.FloatField()),
                ('total_lm', models.FloatField()),
                ('gmv_per_day', models.FloatField()),
                ('gmv_per_lm', models.FloatField()),
                ('lm_contribution_in_store', models.FloatField()),
                ('outlier_status', models.CharField(max_length=20)),
                ('suggested_total_lm', models.FloatField()),
                ('last_update_dt_tm', models.DateTimeField()),
                ('outlier_status_final', models.CharField(blank=True, max_length=20, null=True)),
            ],
            options={
                'db_table': 'app_hb_preopt',
            },
        ),
    ]
